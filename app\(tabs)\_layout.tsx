import { Tabs } from "expo-router";
import { Dimensions, Image, ImageBackground, Text, View } from "react-native";
import {
  SafeAreaProvider,
  useSafeAreaInsets,
} from "react-native-safe-area-context";
import { icons } from "../../constants/icons";
import { images } from "../../constants/images";

interface TabIconProps {
  focused: boolean;
  icon: any;
  title: string;
}

const { width: screenWidth } = Dimensions.get("window");
const isSmallScreen = screenWidth < 375; // iPhone SE and smaller
const isLargeScreen = screenWidth > 414; // iPhone Pro Max and larger

const TabIcon = ({ focused, icon, title }: TabIconProps) => {
  if (focused) {
    return (
      <ImageBackground
        source={images.highlight}
        className="flex flex-row items-center justify-center rounded-full overflow-hidden"
        style={{
          width: "100%",
          height: isSmallScreen ? 48 : 56,
          marginTop: isSmallScreen ? 8 : 12,
          paddingHorizontal: 8,
          minWidth: isSmallScreen ? 80 : isLargeScreen ? 120 : 100,
          maxWidth: screenWidth * 0.22, // Responsive max width based on screen
        }}
      >
        <Image
          source={icon}
          tintColor={"#151312"}
          className={isSmallScreen ? "size-4" : "size-5"}
        />
        <Text
          className={`text-secondary font-semibold ml-2 ${
            isSmallScreen ? "text-sm" : "text-base"
          }`}
          numberOfLines={1}
          adjustsFontSizeToFit
        >
          {title}
        </Text>
      </ImageBackground>
    );
  }
  return (
    <View
      className="justify-center items-center rounded-full"
      style={{
        width: "100%",
        height: isSmallScreen ? 48 : 56,
        marginTop: isSmallScreen ? 8 : 12,
      }}
    >
      <Image
        source={icon}
        tintColor={"#A8B5DB"}
        className={isSmallScreen ? "size-4" : "size-5"}
      />
    </View>
  );
};

const TabsLayout = () => {
  const insets = useSafeAreaInsets(); // <-- get safe bottom padding

  // Responsive tab bar dimensions
  const tabBarHeight = isSmallScreen ? 56 : isLargeScreen ? 64 : 60;
  const horizontalMargin = isSmallScreen ? 12 : isLargeScreen ? 24 : 16;
  const bottomMargin = insets.bottom + (isSmallScreen ? 8 : 12);

  return (
    <Tabs
      screenOptions={{
        tabBarShowLabel: false,
        tabBarItemStyle: {
          width: "100%",
          height: "100%",
          justifyContent: "center",
          alignItems: "center",
          paddingHorizontal: isSmallScreen ? 2 : 4,
        },
        tabBarStyle: {
          backgroundColor: "#0f0d23",
          borderRadius: isSmallScreen ? 28 : 32,
          marginHorizontal: horizontalMargin,
          marginBottom: bottomMargin,
          height: tabBarHeight,
          position: "absolute",
          borderWidth: 1,
          borderColor: "#0f0d23",
          overflow: "hidden",
          // Add subtle shadow for better visual hierarchy
          shadowColor: "#000",
          shadowOffset: {
            width: 0,
            height: 4,
          },
          shadowOpacity: 0.3,
          shadowRadius: 8,
          elevation: 8,
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: "Home",
          headerShown: false,
          tabBarIcon: ({ focused }) => (
            <TabIcon focused={focused} icon={icons.home} title="Home" />
          ),
        }}
      />
      <Tabs.Screen
        name="search"
        options={{
          title: "Search",
          headerShown: false,
          tabBarIcon: ({ focused }) => (
            <TabIcon focused={focused} icon={icons.search} title="Search" />
          ),
        }}
      />
      <Tabs.Screen
        name="saved"
        options={{
          title: "Saved",
          headerShown: false,
          tabBarIcon: ({ focused }) => (
            <TabIcon focused={focused} icon={icons.save} title="Saved" />
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: "Profile",
          headerShown: false,
          tabBarIcon: ({ focused }) => (
            <TabIcon focused={focused} icon={icons.person} title="Profile" />
          ),
        }}
      />
    </Tabs>
  );
};

export default function Layout() {
  return (
    <SafeAreaProvider>
      <TabsLayout />
    </SafeAreaProvider>
  );
}
